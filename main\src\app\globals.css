@import "tailwindcss";

:root {
  --background: #ffffff;
  --foreground: #171717;
}

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --font-sans: var(--font-geist-sans);
  --font-mono: var(--font-geist-mono);
}

@media (prefers-color-scheme: dark) {
  :root {
    --background: #0a0a0a;
    --foreground: #ededed;
  }
}

body {
  background: var(--background);
  color: var(--foreground);
  font-family: Arial, Helvetica, sans-serif;
  overflow-x: hidden; /* Prevent horizontal scrolling */
  width: 100%;
}

html {
  overflow-x: hidden; /* Ensure the html element also prevents overflow */
}

@keyframes slideDown {
  from {
    transform: translateY(-200%);
    opacity: 0;
  }
  to {
    transform: translateY(-50%);
    opacity: 1;
  }
}

@keyframes slideDownOut {
  from {
    transform: translateY(-50%);
    opacity: 1;
  }
  to {
    transform: translateY(200%);
    opacity: 0;
  }
}

.transition-all {
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
}

/* Add these styles for the animated circles */
.animate-circle {
  transform-origin: center;
  will-change: transform, opacity;
}

@keyframes moveCircle {
  0% {
    transform: translateX(0) translateY(0);
    opacity: 0;
  }
  25% {
    transform: translateX(100px) translateY(-10px);
    opacity: 1;
  }
  75% {
    transform: translateX(200px) translateY(10px);
    opacity: 1;
  }
  100% {
    transform: translateX(300px) translateY(0);
    opacity: 0;
  }
}

/* Add these animations to your existing CSS */
@keyframes dropdownOpen {
  from {
    opacity: 0;
    transform: translateY(-8px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes dropdownClose {
  from {
    opacity: 1;
    transform: translateY(0);
  }
  to {
    opacity: 0;
    transform: translateY(-8px);
  }
}

/* Add these utility classes */
.dropdown-open {
  animation: dropdownOpen 0.2s ease-out forwards;
}

.dropdown-close {
  animation: dropdownClose 0.2s ease-out forwards;
}

/* Add smooth transitions to existing dropdowns */
select,
.dropdown-content {
  transition: all 0.2s ease-out;
}

/* Hide footer and bottom navigation when search page is active */
body[data-search-active="true"] footer,
body[data-search-active="true"] nav.fixed.bottom-0 {
  display: none !important;
}

/* Add a full-screen white background when search is active */
body[data-search-active="true"]::before {
  content: "";
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  background-color: white;
  z-index: 9998;
}

/* Ensure the search results page is above everything */
body[data-search-active="true"] [data-search-results="true"] {
  z-index: 9999;
  position: relative;
}

/* Fixed search container in hero section */
.fixed-search-container {
  position: absolute;
  z-index: 9999 !important; /* Ensure it's above everything */
}

/* Search container styles */
.search-container {
  position: relative;
  z-index: 30;
  background-color: white;
  border-radius: 0.5rem;
}

/* Ensure search bar doesn't go above nav when scrolling */
@media (min-width: 768px) {
  .search-container {
    position: relative !important;
    top: auto !important;
  }
}

/* Hide scrollbar for horizontal scrolling */
.scrollbar-hide {
  -ms-overflow-style: none;  /* Internet Explorer 10+ */
  scrollbar-width: none;  /* Firefox */
}

.scrollbar-hide::-webkit-scrollbar {
  display: none;  /* Safari and Chrome */
}
