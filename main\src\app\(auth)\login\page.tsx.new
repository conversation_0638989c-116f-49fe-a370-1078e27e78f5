"use client";

import React, { useState } from 'react';
import PageWrapper from '@/components/PageWrapper';
import Link from 'next/link';
import Image from 'next/image';
import { motion } from 'framer-motion';
import { useRouter, useSearchParams } from 'next/navigation';
import { toast } from 'react-hot-toast';
import { useAuth } from '@/context/AuthContext';
import SocialLoginButtons from '@/components/auth/SocialLoginButtons';
import { FaHome } from 'react-icons/fa';

const Login = () => {
  const router = useRouter();
  const searchParams = useSearchParams();
  const message = searchParams.get('message');
  const error = searchParams.get('error');
  const [isLoading, setIsLoading] = useState(false);
  const [loginMethod, setLoginMethod] = useState<'email' | 'phone'>('email');
  const [formData, setFormData] = useState({
    email: '',
    phone: '',
    password: ''
  });
  const { login } = useAuth();
  
  // Show message if redirected with a message parameter
  React.useEffect(() => {
    if (message) {
      toast.success(message);
    }
    if (error) {
      // Handle error messages from social auth
      const errorMessages = {
        'google_auth_failed': 'Google authentication failed',
        'facebook_auth_failed': 'Facebook authentication failed',
        'server_error': 'Server error occurred',
      };
      toast.error(errorMessages[error as keyof typeof errorMessages] || 'Authentication error');
    }
  }, [message, error]);

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setFormData({
      ...formData,
      [e.target.name]: e.target.value
    });
  };

  const handleLogin = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsLoading(true);
    
    try {
      // Prepare the request body based on login method
      const requestBody = loginMethod === 'email'
        ? { email: formData.email, password: formData.password }
        : { phone: formData.phone, password: formData.password };
      
      const response = await fetch('/api/auth/login', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(requestBody),
      });

      const data = await response.json();

      if (response.ok) {
        // Update auth context with user data
        login(data.user);
        router.push('/dashboard'); // Redirect to dashboard or home
      } else {
        toast.error(data.error || 'Login failed');
      }
    } catch (error) {
      console.error('Login error:', error);
      toast.error('Something went wrong. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <PageWrapper>
      <div className="min-h-screen flex items-center justify-center relative">
        {/* Background Image (Full Screen) */}
        <div className="absolute inset-0 w-full h-screen">
          <div className="h-full w-full">
            <Image
              src="/auth/Login Art.png"
              alt="Login Background"
              fill
              style={{ objectFit: 'cover' }}
              priority
              className="hidden md:block"
            />
            <Image
              src="/auth/Login Art Mobile.png"
              alt="Login Background Mobile"
              fill
              style={{ objectFit: 'cover' }}
              priority
              className="block md:hidden"
            />
          </div>
        </div>

        {/* Login Form Container */}
        <div className="w-full max-w-md z-10 relative px-4 md:px-0">
          <motion.div 
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5 }}
            className="bg-white p-6 rounded-lg w-full"
          >
            <h1 className="text-xl font-semibold text-gray-800 mb-1">
              Welcome Back <span className="text-yellow-500">👋</span>
            </h1>
            <p className="text-gray-500 text-sm mb-5">
              Enter your credentials below & access your account
            </p>

            <div className="mb-6">
              <div className="flex space-x-4 mb-4">
                <button
                  type="button"
                  onClick={() => setLoginMethod('email')}
                  className={`flex-1 py-2 border-b-2 ${loginMethod === 'email' ? 'border-blue-800 text-blue-800' : 'border-gray-200 text-gray-500 hover:text-blue-800 hover:border-blue-800'} font-medium transition-colors`}
                >
                  Email
                </button>
                <button
                  type="button"
                  onClick={() => setLoginMethod('phone')}
                  className={`flex-1 py-2 border-b-2 ${loginMethod === 'phone' ? 'border-blue-800 text-blue-800' : 'border-gray-200 text-gray-500 hover:text-blue-800 hover:border-blue-800'} font-medium transition-colors`}
                >
                  Phone
                </button>
              </div>

              <form onSubmit={handleLogin} className="space-y-5">
                {loginMethod === 'email' ? (
                  <div>
                    <label htmlFor="email" className="block text-sm font-medium text-gray-700 mb-1">Email</label>
                    <input
                      id="email"
                      type="email"
                      name="email"
                      value={formData.email}
                      onChange={handleChange}
                      placeholder="<EMAIL>"
                      className="w-full p-2.5 bg-gray-50 rounded-md border border-gray-300 focus:border-blue-800 focus:ring-1 focus:ring-blue-800 transition-all"
                      required
                    />
                  </div>
                ) : (
                  <div>
                    <label htmlFor="phone" className="block text-sm font-medium text-gray-700 mb-1">Phone Number</label>
                    <input
                      id="phone"
                      type="tel"
                      name="phone"
                      value={formData.phone}
                      onChange={handleChange}
                      placeholder="+****************"
                      className="w-full p-2.5 bg-gray-50 rounded-md border border-gray-300 focus:border-blue-800 focus:ring-1 focus:ring-blue-800 transition-all"
                      required
                    />
                  </div>
                )}

                <div>
                  <label htmlFor="password" className="block text-sm font-medium text-gray-700 mb-1">Password</label>
                  <input
                    id="password"
                    type="password"
                    name="password"
                    value={formData.password}
                    onChange={handleChange}
                    placeholder="••••••••"
                    className="w-full p-2.5 bg-gray-50 rounded-md border border-gray-300 focus:border-blue-800 focus:ring-1 focus:ring-blue-800 transition-all"
                    required
                  />
                </div>

                <div className="flex items-center justify-between mb-1">
                  <div className="flex items-center">
                    <input
                      id="remember-me"
                      name="remember-me"
                      type="checkbox"
                      className="h-4 w-4 text-blue-800 focus:ring-blue-800 border-gray-300 rounded"
                    />
                    <label htmlFor="remember-me" className="ml-2 block text-sm text-gray-700">
                      Remember me
                    </label>
                  </div>
                  <Link href="/forgot-password" className="text-sm font-medium text-blue-800 hover:text-blue-700 transition-colors">
                    Forgot password?
                  </Link>
                </div>
                
                <motion.button
                  whileHover={{ scale: 1.02 }}
                  whileTap={{ scale: 0.98 }}
                  type="submit"
                  disabled={isLoading}
                  className="w-full bg-blue-800 p-2.5 rounded-md font-medium text-white hover:bg-blue-900 transition-all disabled:opacity-70 disabled:cursor-not-allowed"
                >
                  {isLoading ? 'Logging in...' : 'Log in'}
                </motion.button>
              </form>
            </div>
            
            <div className="mt-6 text-center">
              <p className="text-gray-600">
                Don't have an account?{' '}
                <Link href="/sign-up" className="text-blue-800 hover:text-blue-700 font-medium transition-colors">
                  Sign up
                </Link>
              </p>
            </div>
            
            <div className="relative my-6">
              <div className="absolute inset-0 flex items-center">
                <div className="w-full border-t border-gray-300"></div>
              </div>
              <div className="relative flex justify-center text-sm">
                <span className="px-2 bg-white text-gray-500">or login with</span>
              </div>
            </div>

            {/* Social Login Buttons */}
            <SocialLoginButtons />
          </motion.div>
        </div>
      </div>
    </PageWrapper>
  );
};

export default Login;
