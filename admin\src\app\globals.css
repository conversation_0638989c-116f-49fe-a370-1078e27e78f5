@import "tailwindcss";

@layer utilities {
  .placeholder-dark::placeholder {
    color: #4b5563 !important; /* gray-600 */
    opacity: 1;
  }
}

:root {
  --background: #ffffff;
  --foreground: #171717;
  --sidebar-gradient-from: #f0f9ff;
  --sidebar-gradient-to: #e0f2fe;
  --sidebar-active: #bfdbfe;
  --card-bg: #ffffff;
  --primary: #3b82f6;
  --primary-light: #93c5fd;
  --success: #10b981;
  --warning: #f59e0b;
  --danger: #ef4444;
  --gray-light: #f3f4f6;
  --gray-medium: #9ca3af;
  --gray-dark: #4b5563;
}

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --font-sans: var(--font-geist-sans);
  --font-mono: var(--font-geist-mono);
}

@media (prefers-color-scheme: dark) {
  :root {
    --background: #0a0a0a;
    --foreground: #ededed;
    --sidebar-gradient-from: #1e293b;
    --sidebar-gradient-to: #0f172a;
    --sidebar-active: #334155;
    --card-bg: #1e1e1e;
    --primary: #3b82f6;
    --primary-light: #60a5fa;
    --gray-light: #374151;
    --gray-medium: #9ca3af;
    --gray-dark: #d1d5db;
  }
}

body {
  background: var(--background);
  color: var(--foreground);
  font-family: var(--font-sans, Arial, Helvetica, sans-serif);
}

/* Dashboard specific styles */
.dashboard-card {
  background-color: var(--card-bg);
  border-radius: 0.5rem;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  transition: all 0.2s ease;
}

.dashboard-card:hover {
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.stats-value {
  font-size: 1.5rem;
  font-weight: 700;
  color: var(--foreground);
}

.stats-change-positive {
  color: var(--success);
  display: flex;
  align-items: center;
}

.stats-change-negative {
  color: var(--danger);
  display: flex;
  align-items: center;
}

.chart-container {
  width: 100%;
  height: 300px;
}

.sidebar-gradient {
  background: linear-gradient(to bottom, var(--sidebar-gradient-from), var(--sidebar-gradient-to));
}

.sidebar-link {
  display: flex;
  align-items: center;
  padding: 0.75rem 1rem;
  border-radius: 0.375rem;
  color: var(--gray-dark);
  transition: all 0.2s ease;
}

.sidebar-link:hover {
  background-color: var(--sidebar-active);
}

.sidebar-link.active {
  background-color: var(--sidebar-active);
  color: var(--primary);
}

.top-nav {
  background-color: var(--card-bg);
  border-bottom: 1px solid rgba(0, 0, 0, 0.1);
  padding: 0.75rem 1.5rem;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.search-input {
  background-color: var(--gray-light);
  border: 1px solid transparent;
  border-radius: 0.375rem;
  padding: 0.5rem 1rem 0.5rem 2.5rem;
  transition: all 0.2s ease;
}

.search-input:focus {
  border-color: var(--primary);
  outline: none;
}

.progress-bar {
  height: 0.5rem;
  background-color: var(--gray-light);
  border-radius: 9999px;
  overflow: hidden;
}

.progress-bar-fill {
  height: 100%;
  background-color: var(--primary);
  border-radius: 9999px;
}

/* Make placeholder text darker */
::placeholder {
  color: #4b5563 !important; /* Using gray-600 color */
  opacity: 1;
}
