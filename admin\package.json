{"name": "admin", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "dev:port": "next dev --turbopack --port 3001", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@heroicons/react": "^2.2.0", "framer-motion": "^12.11.0", "jsonwebtoken": "^9.0.2", "lucide-react": "^0.485.0", "next": "15.2.5", "pg": "^8.16.0", "react": "^19.0.0", "react-dom": "^19.0.0", "react-hot-toast": "^2.5.2", "recharts": "^2.15.3", "redis": "^5.5.6", "uuid": "^11.1.0"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@types/jsonwebtoken": "^9.0.9", "@types/node": "^20.17.30", "@types/pg": "^8.15.4", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "15.2.5", "tailwindcss": "^4", "typescript": "^5"}}